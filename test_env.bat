@echo off
setlocal enabledelayedexpansion

REM 设置UTF-8编码
chcp 65001 >nul 2>&1

title 环境测试

echo.
echo ========================================
echo    环境诊断工具
echo ========================================
echo.

REM 检查Python
echo [测试1] 检查Python...
python --version 2>nul
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo.
    echo 解决方案:
    echo 1. 下载并安装Python 3.7+: https://www.python.org/downloads/
    echo 2. 安装时勾选 "Add Python to PATH"
    echo 3. 重启命令提示符
    echo.
    goto :end
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do echo ✅ Python版本: %%i
)

REM 检查pip
echo.
echo [测试2] 检查pip...
python -m pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip不可用
    echo.
    echo 解决方案:
    echo python -m ensurepip --upgrade
    echo.
    goto :end
) else (
    echo ✅ pip可用
)

REM 检查必要文件
echo.
echo [测试3] 检查项目文件...
set "missing_files="
if not exist "main.py" set "missing_files=!missing_files! main.py"
if not exist "start.py" set "missing_files=!missing_files! start.py"
if not exist "requirements.txt" set "missing_files=!missing_files! requirements.txt"

if not "!missing_files!"=="" (
    echo ❌ 缺少文件:!missing_files!
    echo.
    echo 请确保在正确的项目目录中运行此脚本
    echo.
    goto :end
) else (
    echo ✅ 项目文件完整
)

REM 运行Python环境测试
echo.
echo [测试4] 运行详细环境测试...
python test_environment.py

goto :end

:end
echo.
echo 按任意键退出...
pause >nul
