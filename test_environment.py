#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境测试脚本
用于诊断运行环境问题
"""

import sys
import os
import platform
import subprocess

def test_python_version():
    """测试Python版本"""
    print("=" * 50)
    print("Python环境测试")
    print("=" * 50)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"架构: {platform.machine()}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查Python版本
    version_info = sys.version_info
    if version_info.major == 3 and version_info.minor >= 7:
        print("✅ Python版本符合要求 (3.7+)")
        return True
    else:
        print("❌ Python版本不符合要求，需要3.7+")
        return False

def test_file_existence():
    """测试必要文件是否存在"""
    print("\n" + "=" * 50)
    print("文件存在性测试")
    print("=" * 50)
    
    required_files = [
        'main.py',
        'start.py', 
        'requirements.txt',
        'config.py',
        'user_manager.py',
        'checkin_manager.py',
        'weibo_client.py'
    ]
    
    all_exist = True
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} (缺失)")
            all_exist = False
    
    return all_exist

def test_dependencies():
    """测试依赖包"""
    print("\n" + "=" * 50)
    print("依赖包测试")
    print("=" * 50)
    
    required_packages = [
        ('requests', 'requests'),
        ('beautifulsoup4', 'bs4'),
        ('schedule', 'schedule'),
        ('lxml', 'lxml'),
        ('fake-useragent', 'fake_useragent'),
        ('cryptography', 'cryptography')
    ]
    
    all_installed = True
    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"✅ {package_name}")
        except ImportError:
            print(f"❌ {package_name} (未安装)")
            all_installed = False
    
    return all_installed

def test_encoding():
    """测试编码支持"""
    print("\n" + "=" * 50)
    print("编码支持测试")
    print("=" * 50)
    
    try:
        # 测试中文字符
        test_str = "微博超话自动签到程序 🎯"
        print(f"中文测试: {test_str}")
        print(f"系统编码: {sys.getdefaultencoding()}")
        print(f"文件系统编码: {sys.getfilesystemencoding()}")
        print("✅ 编码支持正常")
        return True
    except Exception as e:
        print(f"❌ 编码支持异常: {e}")
        return False

def test_pip():
    """测试pip是否可用"""
    print("\n" + "=" * 50)
    print("pip测试")
    print("=" * 50)
    
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ pip版本: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ pip执行失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ pip测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 开始环境诊断...")
    print()
    
    results = []
    
    # 运行各项测试
    results.append(("Python版本", test_python_version()))
    results.append(("文件存在性", test_file_existence()))
    results.append(("依赖包", test_dependencies()))
    results.append(("编码支持", test_encoding()))
    results.append(("pip工具", test_pip()))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！环境配置正常")
        print("您可以正常运行 start.bat 或 python start.py")
    else:
        print("⚠️  部分测试失败，请根据上述结果修复问题")
        print("\n常见解决方案:")
        print("1. 确保Python 3.7+已正确安装")
        print("2. 运行: pip install -r requirements.txt")
        print("3. 确保在正确的项目目录中运行")
    
    print("\n按任意键退出...")
    try:
        input()
    except:
        pass

if __name__ == "__main__":
    main()
