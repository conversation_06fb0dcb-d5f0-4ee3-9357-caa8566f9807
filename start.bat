@echo off
setlocal enabledelayedexpansion

REM 设置UTF-8编码支持中文
chcp 65001 >nul 2>&1

REM 设置窗口标题
title 微博超话自动签到程序

REM 显示欢迎信息
echo.
echo ========================================
echo    微博超话自动签到程序
echo ========================================
echo.

REM 检查当前目录是否包含必要文件
if not exist "start.py" (
    echo [错误] 未找到 start.py 文件
    echo 请确保在正确的程序目录中运行此脚本
    echo.
    pause
    exit /b 1
)

if not exist "main.py" (
    echo [错误] 未找到 main.py 文件
    echo 请确保在正确的程序目录中运行此脚本
    echo.
    pause
    exit /b 1
)

REM 检查Python是否安装
echo [检查] 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未检测到Python，请先安装Python 3.7+
    echo.
    echo 下载地址: https://www.python.org/downloads/
    echo.
    echo 安装完成后，请确保Python已添加到系统PATH环境变量中
    echo.
    pause
    exit /b 1
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
    echo [成功] 检测到Python版本: !PYTHON_VERSION!
)

echo.
echo [启动] 正在启动程序...
echo.

REM 运行启动脚本
python start.py

REM 检查启动脚本的退出代码
if errorlevel 1 (
    echo.
    echo [错误] 程序启动失败，退出代码: %errorlevel%
    echo.
)

echo.
echo 按任意键退出...
pause >nul
