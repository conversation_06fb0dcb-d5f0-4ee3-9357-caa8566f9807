"""
微博超话自动签到程序启动脚本
"""
import os
import sys
import subprocess


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True


def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        ('requests', 'requests'),
        ('beautifulsoup4', 'bs4'),
        ('schedule', 'schedule'),
        ('lxml', 'lxml'),
        ('fake-useragent', 'fake_useragent'),
        ('cryptography', 'cryptography')
    ]

    missing_packages = []
    print("📦 正在检查依赖包...")

    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"   ✅ {package_name}")
        except ImportError:
            missing_packages.append(package_name)
            print(f"   ❌ {package_name} (缺失)")

    if missing_packages:
        print(f"\n❌ 缺少 {len(missing_packages)} 个依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False

    print("✅ 所有依赖包检查完成")
    return True


def install_dependencies():
    """安装依赖"""
    print("📦 正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败")
        return False


def create_directories():
    """创建必要的目录"""
    directories = ['logs']
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"📁 创建目录: {directory}")


def show_welcome():
    """显示欢迎信息"""
    print("🎯 微博超话自动签到程序")
    print("=" * 50)
    print("版本: 1.0.0")
    print("作者: AI Assistant")
    print("功能: 自动签到微博超话，支持定时任务")
    print("=" * 50)


def show_usage():
    """显示使用说明"""
    print("\n📋 使用说明:")
    print("1. 首次使用需要设置微博登录cookies")
    print("2. 可以自动发现关注的超话或手动添加")
    print("3. 支持一次性签到或设置定时任务")
    print("4. 所有操作都有详细的日志记录")
    print("\n🚀 启动方式:")
    print("   交互模式: python main.py --interactive")
    print("   命令行模式: python main.py --help")


def main():
    """主函数"""
    try:
        show_welcome()

        # 检查Python版本
        print("🔍 检查Python版本...")
        if not check_python_version():
            print("\n按任意键退出...")
            input()
            return
        print("✅ Python版本检查通过")

        # 检查依赖
        if not check_dependencies():
            choice = input("\n是否自动安装依赖? (y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                if not install_dependencies():
                    print("\n按任意键退出...")
                    input()
                    return
                # 重新检查依赖
                if not check_dependencies():
                    print("❌ 依赖安装后仍有问题，请手动检查")
                    print("\n按任意键退出...")
                    input()
                    return
            else:
                print("请手动安装依赖后再运行程序")
                print("命令: pip install -r requirements.txt")
                print("\n按任意键退出...")
                input()
                return

        # 创建必要目录
        create_directories()

        # 显示使用说明
        show_usage()

        # 询问启动方式
        print("\n🎮 选择启动方式:")
        print("1. 交互模式 (推荐)")
        print("2. 查看帮助")
        print("3. 运行测试")
        print("4. 退出")

        while True:
            try:
                choice = input("\n请选择 (1-4): ").strip()

                if choice == '1':
                    print("\n🚀 启动交互模式...")
                    result = os.system(f'"{sys.executable}" main.py --interactive')
                    if result != 0:
                        print(f"❌ 程序执行失败，退出代码: {result}")
                    break
                elif choice == '2':
                    print("\n📖 显示帮助信息...")
                    os.system(f'"{sys.executable}" main.py --help')
                    break
                elif choice == '3':
                    print("\n🧪 运行基本测试...")
                    if os.path.exists("test_basic.py"):
                        os.system(f'"{sys.executable}" test_basic.py')
                    else:
                        print("❌ 测试文件不存在")
                    break
                elif choice == '4':
                    print("👋 再见!")
                    break
                else:
                    print("❌ 无效选择，请输入1-4")

            except KeyboardInterrupt:
                print("\n👋 程序被中断，再见!")
                break

    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        print("\n按任意键退出...")
        input()
        sys.exit(1)


if __name__ == "__main__":
    main()
